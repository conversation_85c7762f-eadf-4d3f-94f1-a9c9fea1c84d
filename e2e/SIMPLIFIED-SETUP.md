# Simplified Playwright Electron Setup

This document describes the simplified e2e testing setup that leverages <PERSON><PERSON>'s native Electron capabilities.

## Key Improvements

### 🎯 **Reduced Complexity**
- **Before**: 500+ lines across multiple helper files
- **After**: ~200 lines in a single setup file
- **Removed**: Complex worker management, custom HAR recording, extensive plugin configuration

### 🚀 **Native Playwright Features**
- **Process Management**: <PERSON><PERSON> handles Electron lifecycle automatically
- **Worker Isolation**: Built-in parallel test execution without custom context management
- **HAR Recording**: Native network request recording with simple configuration
- **Timeout Handling**: Automatic timeout management with sensible defaults

### 🧪 **Simplified Test Writing**
- **Before**: Complex setup with shared contexts and multiple helper imports
- **After**: Single import with intuitive API
- **Better**: Clear separation of concerns and easier debugging

## Migration Guide

### 1. **Replace Complex Setup**

**Before (complex):**
```typescript
import { getSharedTestContext, resetSharedTestContext } from '../helpers/shared-context';
import { setupObsidianElectron, verifyPluginAvailable } from '../helpers/plugin-setup';
import { completeModalInteraction } from '../helpers/modal-helpers';

let context: SharedTestContext;
beforeAll(async () => { context = await getSharedTestContext(); });
beforeEach(async () => { await resetSharedTestContext(); });
```

**After (simplified):**
```typescript
import { setupTest, resetUI, type ElectronTestContext } from '../helpers/simplified-electron-setup';

let context: ElectronTestContext;
beforeAll(async () => { context = await setupTest(); });
beforeEach(async () => { await resetUI(context.page); });
```

### 2. **Simplified Modal Interactions**

**Before:**
```typescript
await completeModalInteraction(
  context.page,
  'create-post',
  { 'post-title': testTitle },
  'submit'
);
```

**After:**
```typescript
await executeCommand(context.page, 'Ghost Sync: Create new post');
await waitForModal(context.page);
await fillModalField(context.page, 'post-title', testTitle);
await submitModal(context.page);
```

### 3. **Native HAR Recording**

**Before:** Complex custom network interception
**After:**
```typescript
const context = await setupTest({
  recordHar: true,
  harPath: './e2e/recordings/my-test.har'
});
// All network requests automatically recorded!
```

## File Structure Changes

### ✅ **New Files**
```
e2e/helpers/simplified-electron-setup.ts  # Single setup file
e2e/specs/simplified-example.e2e.ts       # Example test
e2e/setup-har-directory.ts                # Simple HAR setup
vitest.simplified.config.mjs               # Simplified config
```

### 🗑️ **Files That Can Be Removed** (after migration)
```
e2e/helpers/shared-context.ts              # Complex context management
e2e/helpers/electron-setup.ts              # Redundant setup
e2e/global-setup.ts                        # Not needed with Playwright
e2e/global-teardown.ts                     # Playwright handles cleanup
e2e/test-environments/                     # Playwright handles isolation
```

### 📝 **Files to Update**
```
e2e/helpers/plugin-setup.ts               # Simplify or remove
e2e/helpers/modal-helpers.ts               # Simplify
vitest.playwright.config.mjs               # Update or replace
```

## Running Tests

### **New Commands**
```bash
# Run simplified tests
npm run test:e2e:simplified

# Run with HAR recording
RECORD_HAR=true npm run test:e2e:simplified

# Clean HAR files before running
CLEAN_HAR_FILES=true npm run test:e2e:simplified
```

### **Add to package.json**
```json
{
  "scripts": {
    "test:e2e:simplified": "npx vitest run --config vitest.simplified.config.mjs",
    "test:e2e:simplified:watch": "npx vitest --config vitest.simplified.config.mjs",
    "test:e2e:simplified:ui": "npx vitest --ui --config vitest.simplified.config.mjs"
  }
}
```

## Benefits

### 🔧 **For Developers**
- **Easier to understand**: Single setup file vs multiple complex helpers
- **Faster to write tests**: Intuitive API with less boilerplate
- **Better debugging**: Native Playwright tools and HAR files
- **Less maintenance**: Fewer custom abstractions to maintain

### 🏃‍♂️ **For CI/CD**
- **Faster execution**: Less setup overhead
- **More reliable**: Playwright's battle-tested process management
- **Better artifacts**: Native HAR recording for debugging failures
- **Simpler configuration**: Standard Playwright patterns

### 🧪 **For Testing**
- **True isolation**: Playwright's native worker isolation
- **Parallel execution**: Better performance with multiple workers
- **Network recording**: Automatic request/response capture
- **Standard patterns**: Follows Playwright best practices

## Migration Strategy

### **Phase 1: Parallel Testing** ✅
1. Add simplified setup alongside existing tests
2. Create example tests using new approach
3. Validate functionality and performance

### **Phase 2: Gradual Migration**
1. Port critical tests to simplified setup
2. Compare results and fix any issues
3. Update CI configuration

### **Phase 3: Complete Migration**
1. Remove old complex setup files
2. Update all tests to use simplified approach
3. Clean up package.json scripts

## Troubleshooting

### **Common Issues**

1. **HAR files not created**
   ```bash
   # Ensure directory exists
   mkdir -p e2e/recordings
   ```

2. **Tests timeout**
   ```typescript
   // Increase timeout if needed
   const context = await setupTest({ timeout: 60000 });
   ```

3. **Plugin not loading**
   ```bash
   # Ensure Obsidian is unpacked
   npm run setup:obsidian-playwright
   ```

### **Debug Tips**

```typescript
// Enable Playwright debug mode
process.env.DEBUG = 'pw:*';

// Record video for debugging
const context = await setupTest({
  recordVideo: { dir: './e2e/videos' }
});
```

## Next Steps

1. **Try the simplified setup**:
   ```bash
   npx vitest run --config vitest.simplified.config.mjs e2e/specs/simplified-example.e2e.ts
   ```

2. **Review HAR recordings**:
   - Check `e2e/recordings/` for network request logs
   - Open HAR files in browser dev tools for analysis

3. **Migrate existing tests**:
   - Start with simple tests
   - Use the patterns from `simplified-example.e2e.ts`

4. **Update CI configuration**:
   - Add simplified test commands
   - Configure HAR artifact collection
