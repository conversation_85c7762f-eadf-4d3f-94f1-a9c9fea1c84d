import type { Page, ElectronApplication } from 'playwright';

/**
 * Shared helper for e2e tests to verify plugin availability
 * This should be called once in beforeAll to ensure the plugin is loaded
 * and avoid redundant checks in individual test functions
 *
 * Updated to work with both CDP and Playwright/Electron setups
 */

/**
 * Wait for async operations to complete
 */
export async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Load environment settings from .env file and process.env
 */
function loadEnvironmentSettings(): { ghostUrl?: string; ghostAdminApiKey?: string } {
  const fs = require('fs');
  const path = require('path');

  const envSettings: { ghostUrl?: string; ghostAdminApiKey?: string } = {};

  // Try to read from .env file
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=').trim();
            // Remove quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            if (key.trim() === 'GHOST_URL') {
              envSettings.ghostUrl = value;
            } else if (key.trim() === 'GHOST_ADMIN_API_KEY') {
              envSettings.ghostAdminApiKey = value;
            }
          }
        }
      }
    } catch (error) {
      console.log('⚠️ Could not read .env file:', error.message);
    }
  }

  // Override with process.env if available
  if (process.env.GHOST_URL) {
    envSettings.ghostUrl = process.env.GHOST_URL;
  }
  if (process.env.GHOST_ADMIN_API_KEY) {
    envSettings.ghostAdminApiKey = process.env.GHOST_ADMIN_API_KEY;
  }

  return envSettings;
}

/**
 * Setup Obsidian with Playwright/Electron
 * Launches Obsidian, waits for initialization, and enables the ghost-sync plugin
 * @param vaultPath Required vault path for testing
 * @param dataDir Required data directory for testing
 */
export async function setupObsidianElectron(
  vaultPath: string,
  dataDir: string
): Promise<{ electronApp: ElectronApplication; page: Page }> {
  const playwright = await import('playwright');
  const electron = playwright._electron;
  const path = await import('path');
  const fs = await import('fs');

  // Read environment variables from .env file if it exists
  const envSettings = loadEnvironmentSettings();

  const appPath = path.resolve('./.obsidian-unpacked/main.js');
  const resolvedVaultPath = path.resolve(vaultPath);
  const userDataDir = path.resolve(dataDir);

  // Check if Obsidian is unpacked
  if (!fs.existsSync(appPath)) {
    throw new Error(
      `Unpacked Obsidian not found at ${appPath}. ` +
      'Please run: npm run setup:obsidian-playwright'
    );
  }

  console.log("🚀 Launching unpacked Obsidian with Playwright...");

  // Configure headless mode for CI environments
  const isHeadless = process.env.CI === 'true' || process.env.E2E_HEADLESS !== 'false';
  const launchArgs = [
    appPath,
    '--user-data-dir=' + userDataDir,
    'open',
    `obsidian://open?path=${encodeURIComponent(resolvedVaultPath)}`,
  ];

  // Add headless flags for CI
  if (isHeadless) {
    launchArgs.push(
      '--headless',
      '--disable-gpu',
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--disable-extensions',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding'
    );
  }

  // Launch unpacked Obsidian using Playwright Electron
  const electronApp = await electron.launch({
    args: launchArgs,
    timeout: 30000,
    // Use xvfb for headless mode in CI
    executablePath: isHeadless && process.platform === 'linux' ? 'xvfb-run' : undefined,
  });

  console.log("✅ Obsidian launched successfully");

  // Get the first window
  const page = await electronApp.firstWindow();
  console.log("📱 Got main window, title:", await page.title());

  // Wait for Obsidian to be fully loaded
  await page.waitForFunction(() => {
    return typeof (window as any).app !== 'undefined' &&
      (window as any).app.workspace !== undefined;
  }, { timeout: 15000 });

  console.log("✅ Obsidian app object is ready");

  // First, ensure plugins are enabled but don't enable ghost-sync yet
  await page.evaluate(() => {
    (window as any).app.plugins.setEnable(true);
  });

  await waitForAsyncOperation(200);

  // Pre-configure the plugin settings in Obsidian's data storage before enabling the plugin
  await page.evaluate(async (envSettings) => {
    try {
      // Create test configuration using environment variables if available, otherwise use safe defaults
      const testSettings = {
        ghostUrl: envSettings.ghostUrl || "http://localhost:9999", // Use env var or local mock server
        ghostAdminApiKey: envSettings.ghostAdminApiKey || "1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", // Use env var or properly formatted fake key
        articlesDir: "articles",
        verbose: false, // Disable verbose logging during tests
        testMode: !envSettings.ghostUrl // Only enable test mode if no real Ghost URL is provided
      };

      // Store the settings in Obsidian's plugin data storage
      // This ensures the plugin loads with safe settings from the start
      const app = (window as any).app;
      if (app.vault && app.vault.adapter) {
        const pluginDataPath = '.obsidian/plugins/ghost-sync/data.json';
        try {
          await app.vault.adapter.write(pluginDataPath, JSON.stringify(testSettings));
          console.log("✅ Pre-configured safe plugin settings in data.json");
        } catch (error) {
          console.log("⚠️ Could not write plugin data file:", error.message);
        }
      }

      // Also store in localStorage as backup
      localStorage.setItem('ghost-sync-test-mode', 'true');
      localStorage.setItem('ghost-sync-test-settings', JSON.stringify(testSettings));

      console.log("✅ Safe test settings configured");
    } catch (error) {
      console.log("⚠️ Could not set test settings:", error.message);
    }
  }, envSettings);

  await waitForAsyncOperation(500);

  // Now safely enable the ghost-sync plugin with pre-configured settings
  await page.evaluate(async () => {
    try {
      console.log("🔌 Enabling ghost-sync plugin with safe settings...");
      await (window as any).app.plugins.enablePlugin('ghost-sync');
      console.log("✅ Ghost-sync plugin enabled");
    } catch (error) {
      console.log("⚠️ Plugin enable error (may be expected):", error.message);
      // Don't throw here - plugin might still be functional
    }
  });

  await waitForAsyncOperation(1000); // Give more time for plugin initialization

  // Apply settings to the plugin after it's loaded
  await page.evaluate((envSettings) => {
    try {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (plugin) {
        // Override with environment settings or safe test defaults
        plugin.settings = {
          ghostUrl: envSettings.ghostUrl || "https://test-ghost-instance.example.com",
          ghostAdminApiKey: envSettings.ghostAdminApiKey || "1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
          articlesDir: "articles",
          verbose: false
        };

        // Mark as test mode to prevent network calls only if no real Ghost URL is provided
        plugin.testMode = !envSettings.ghostUrl;

        // Override loadNewsletters to prevent API calls during tests only in test mode
        if (plugin.testMode) {
          plugin.loadNewsletters = async () => {
            console.log("🧪 Test mode: Skipping newsletter loading");
            plugin.newsletters = [];
            plugin.newslettersLoaded = true;
          };

          // Override any other methods that might make API calls during initialization
          plugin.syncCurrentPostToGhost = async () => {
            console.log("🧪 Test mode: Sync operation intercepted");
            return { success: false, error: "Test mode - no actual sync performed" };
          };
        }

        console.log("✅ Applied settings and overrides to plugin", {
          testMode: plugin.testMode,
          ghostUrl: plugin.settings.ghostUrl
        });
      }
    } catch (error) {
      console.log("⚠️ Could not apply settings:", error.message);
    }
  }, envSettings);

  await waitForAsyncOperation(500);

  // Verify plugin is properly loaded (with error handling)
  try {
    await verifyPluginAvailable(page);
  } catch (error) {
    console.log("⚠️ Plugin verification failed, but continuing:", error.message);
    // Don't fail the setup - the plugin might still be partially functional
  }

  return { electronApp, page };
}

/**
 * Wait for a success notice to appear
 */
export async function waitForSuccessNotice(page: Page, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(
      () => {
        const notices = document.querySelectorAll('.notice');
        const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
        return noticeTexts.some(text =>
          text.includes('sync') ||
          text.includes('success') ||
          text.includes('updated') ||
          text.includes('published') ||
          text.includes('saved')
        );
      },
      {},
      { timeout }
    );
    return true;
  } catch (error) {
    console.log(`No success notice appeared within ${timeout}ms`);
    return false;
  }
}

/**
 * Wait for any modal to appear and return modal info
 * Unified approach for all modal types in Obsidian
 */
export async function waitForModal(page: Page, timeout: number = 5000): Promise<{ found: boolean; type: string; element?: any }> {
  try {
    const modalInfo = await page.waitForFunction(
      () => {
        // Check for standard Obsidian modals
        const modalContainer = document.querySelector('.modal-container');
        if (modalContainer) {
          return { found: true, type: 'modal-container' };
        }

        // Check for SuggestModal (used by PostSelectionModal)
        const suggester = document.querySelector('.suggester-container');
        if (suggester) {
          return { found: true, type: 'suggester' };
        }

        // Check for custom modals with modal-backdrop (Svelte components)
        const modalBackdrop = document.querySelector('.modal-backdrop');
        if (modalBackdrop) {
          return { found: true, type: 'custom-modal' };
        }

        return { found: false, type: 'none' };
      },
      {},
      { timeout }
    );

    const result = await modalInfo.jsonValue();
    return result;
  } catch (error) {
    console.log(`No modal appeared within ${timeout}ms`);
    return { found: false, type: 'none' };
  }
}

/**
 * Get modal content for interaction
 * Returns the main content element of any modal type
 */
export async function getModalContent(page: Page): Promise<any> {
  return await page.evaluate(() => {
    // Check for standard Obsidian modals
    const modalContainer = document.querySelector('.modal-container');
    if (modalContainer) {
      return {
        type: 'modal-container',
        element: modalContainer,
        contentElement: modalContainer.querySelector('.modal-content') || modalContainer
      };
    }

    // Check for SuggestModal
    const suggester = document.querySelector('.suggester-container');
    if (suggester) {
      return {
        type: 'suggester',
        element: suggester,
        contentElement: suggester
      };
    }

    // Check for custom modals
    const modalBackdrop = document.querySelector('.modal-backdrop');
    if (modalBackdrop) {
      const prompt = modalBackdrop.querySelector('.prompt');
      const modalContent = modalBackdrop.querySelector('.modal-content');
      return {
        type: 'custom-modal',
        element: modalBackdrop,
        contentElement: prompt || modalContent || modalBackdrop
      };
    }

    return null;
  });
}

/**
 * Reset Obsidian UI state by closing modals and dialogs
 * This ensures clean state between tests without destroying the app context
 */
export async function resetObsidianUI(page: Page): Promise<void> {
  try {
    // Close any open modals using multiple strategies
    await page.evaluate(() => {
      // Strategy 1: Close modals via DOM manipulation
      const modals = document.querySelectorAll('.modal-container, .modal-backdrop, .suggester-container, .prompt, .modal');
      modals.forEach(modal => {
        // Try to find and click close buttons
        const closeButton = modal.querySelector('.modal-close, .close, [aria-label="Close"], button[data-action="close"]');
        if (closeButton) {
          (closeButton as HTMLElement).click();
        } else {
          // Remove modal directly if no close button
          modal.remove();
        }
      });

      // Strategy 2: Clear any overlay elements
      const overlays = document.querySelectorAll('.overlay, .backdrop, .modal-overlay');
      overlays.forEach(overlay => overlay.remove());

      // Strategy 3: Reset focus to main editor
      const editor = document.querySelector('.cm-editor, .CodeMirror, .workspace-leaf-content[data-type="markdown"]');
      if (editor) {
        (editor as HTMLElement).focus();
      }
    });

    // Strategy 4: Use keyboard shortcuts to close any remaining modals
    await page.keyboard.press('Escape');
    await page.keyboard.press('Escape'); // Double escape for nested modals
    await page.keyboard.press('Escape'); // Triple escape to be absolutely sure

    // Give a moment for cleanup to complete
    await waitForAsyncOperation(300);

    console.log('✅ Obsidian UI cleaned successfully');
  } catch (error) {
    console.log(`⚠️ UI cleanup failed: ${error.message}`);
    // Final fallback: just press escape multiple times
    try {
      await page.keyboard.press('Escape');
      await page.keyboard.press('Escape');
      await waitForAsyncOperation(200);
    } catch (fallbackError) {
      console.log(`⚠️ Fallback cleanup also failed: ${fallbackError.message}`);
    }
  }
}

/**
 * Verify that the Ghost Sync plugin is properly loaded and available
 * This should be called once in beforeAll setup
 */
export async function verifyPluginAvailable(page: Page): Promise<void> {
  const pluginCheck = await page.evaluate(() => {
    try {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const isEnabled = (window as any).app.plugins.isEnabled('ghost-sync');

      return {
        success: true,
        pluginExists: !!plugin,
        isEnabled: isEnabled,
        hasSettings: !!plugin?.settings,
        hasSyncMetadata: !!plugin?.syncMetadata,
        pluginMethods: plugin ? Object.keys(plugin).filter(key => typeof plugin[key] === 'function') : [],
        errorMessage: null as string | null
      };
    } catch (error) {
      return {
        success: false,
        pluginExists: false,
        isEnabled: false,
        hasSettings: false,
        hasSyncMetadata: false,
        errorMessage: error.message
      };
    }
  });

  if (!pluginCheck.success) {
    console.log(`⚠️ Plugin verification error: ${pluginCheck.errorMessage}`);
    // Don't throw - just log the error and continue
    return;
  }

  if (!pluginCheck.pluginExists) {
    console.log('⚠️ Ghost Sync plugin is not loaded. This may be due to initialization issues.');
    // Don't throw - plugin might still be partially functional
    return;
  }

  console.log(`✅ Plugin verification passed:`);
  console.log(`  - Plugin exists: ${pluginCheck.pluginExists}`);
  console.log(`  - Plugin enabled: ${pluginCheck.isEnabled}`);
  console.log(`  - Has settings: ${pluginCheck.hasSettings}`);
  console.log(`  - Has sync metadata: ${pluginCheck.hasSyncMetadata}`);
  console.log(`  - Available methods: ${pluginCheck.pluginMethods.length}`);
}

/**
 * Get plugin instance (assumes plugin availability has been verified)
 * This is a simplified version that doesn't include defensive checks
 */
export async function getPlugin(page: Page): Promise<any> {
  return await page.evaluate(() => {
    return (window as any).app.plugins.plugins['ghost-sync'];
  });
}

/**
 * Get file from vault (assumes plugin availability has been verified)
 * This is a simplified version that doesn't include defensive checks
 */
export async function getFile(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(({ path }) => {
    return (window as any).app.vault.getAbstractFileByPath(path);
  }, { path: filePath });
}

/**
 * Get sync metadata for a file (assumes plugin availability has been verified)
 */
export async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(async ({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    const file = (window as any).app.vault.getAbstractFileByPath(path);

    if (!file) {
      throw new Error(`File not found: ${path}`);
    }

    try {
      const syncedAt = plugin.syncMetadata?.getSyncedAt?.(file);
      const changedAt = plugin.syncMetadata?.getChangedAt?.(file);

      return {
        hasSyncedAt: !!syncedAt,
        syncedAt: syncedAt,
        hasChangedAt: !!changedAt,
        changedAt: changedAt,
        fileExists: true,
        pluginExists: true
      };
    } catch (error) {
      throw new Error(`Error getting sync metadata: ${error.message}`);
    }
  }, { path: filePath });
}
