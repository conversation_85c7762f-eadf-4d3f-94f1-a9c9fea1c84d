import { _electron as electron } from 'playwright';
import type { ElectronApplication, Page } from 'playwright';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Simplified Electron Test Setup using <PERSON><PERSON>'s native capabilities
 * 
 * This replaces the complex setup with <PERSON><PERSON>'s built-in features:
 * - Native process management
 * - Built-in worker isolation  
 * - Automatic HAR recording
 * - Standard Electron launch patterns
 */

export interface ElectronTestContext {
  electronApp: ElectronApplication;
  page: Page;
  cleanup: () => Promise<void>;
}

/**
 * Launch Obsidian using <PERSON><PERSON>'s native Electron support
 * Much simpler than the current complex setup
 */
export async function launchObsidian(options: {
  vaultPath?: string;
  dataDir?: string;
  recordHar?: boolean;
  harPath?: string;
} = {}): Promise<ElectronTestContext> {
  
  const {
    vaultPath = './tests/vault/Test',
    dataDir = './e2e/test_obsidian_data',
    recordHar = true,
    harPath = `./e2e/recordings/test-${Date.now()}.har`
  } = options;

  // Ensure directories exist
  const resolvedVaultPath = path.resolve(vaultPath);
  const resolvedDataDir = path.resolve(dataDir);
  
  if (!fs.existsSync(resolvedVaultPath)) {
    throw new Error(`Vault path does not exist: ${resolvedVaultPath}`);
  }

  // Ensure HAR directory exists if recording
  if (recordHar) {
    const harDir = path.dirname(harPath);
    fs.mkdirSync(harDir, { recursive: true });
  }

  // Use Playwright's native Electron launch with minimal configuration
  const electronApp = await electron.launch({
    // Use unpacked Obsidian (from existing setup)
    args: [
      path.resolve('./.obsidian-unpacked/main.js'),
      '--user-data-dir=' + resolvedDataDir,
      'open',
      `obsidian://open?path=${encodeURIComponent(resolvedVaultPath)}`
    ],
    
    // Native HAR recording - much simpler than custom implementation
    recordHar: recordHar ? {
      path: harPath,
      mode: 'minimal', // Only essential info for debugging
      content: 'omit'  // Don't store response bodies to keep files small
    } : undefined,
    
    // Playwright handles headless mode automatically based on environment
    timeout: 30000,
    
    // Environment variables for test mode
    env: {
      ...process.env,
      NODE_ENV: 'test',
      OBSIDIAN_TEST_MODE: 'true'
    }
  });

  // Get the main window - Playwright handles waiting automatically
  const page = await electronApp.firstWindow();
  
  // Wait for Obsidian to be ready - simplified check
  await page.waitForFunction(() => {
    return typeof (window as any).app !== 'undefined' && 
           (window as any).app.workspace !== undefined;
  }, { timeout: 15000 });

  // Enable plugins if needed - much simpler approach
  await page.evaluate(() => {
    const app = (window as any).app;
    if (app.plugins) {
      app.plugins.setEnable(true);
    }
  });

  // Simple plugin enablement - let Obsidian handle the complexity
  await page.evaluate(() => {
    const app = (window as any).app;
    if (app.plugins && !app.plugins.isEnabled('ghost-sync')) {
      try {
        app.plugins.enablePlugin('ghost-sync');
      } catch (error) {
        console.log('Plugin enable failed (may be expected):', error.message);
      }
    }
  });

  // Cleanup function using Playwright's native capabilities
  const cleanup = async () => {
    try {
      await electronApp.close();
    } catch (error) {
      console.log('Cleanup error:', error.message);
    }
  };

  return { electronApp, page, cleanup };
}

/**
 * Simple test helper for common operations
 */
export async function executeCommand(page: Page, command: string): Promise<void> {
  await page.evaluate((cmd) => {
    (window as any).app.commands.executeCommandById(cmd);
  }, command);
}

/**
 * Wait for modal to appear - simplified version
 */
export async function waitForModal(page: Page, timeout = 5000): Promise<boolean> {
  try {
    await page.waitForSelector('.modal-container, .suggester-container, .modal-backdrop', { timeout });
    return true;
  } catch {
    return false;
  }
}

/**
 * Simple modal interaction
 */
export async function fillModalField(page: Page, fieldName: string, value: string): Promise<void> {
  const selector = `[data-testid="${fieldName}"], input[placeholder*="${fieldName}"], input[name="${fieldName}"]`;
  await page.fill(selector, value);
}

/**
 * Submit modal
 */
export async function submitModal(page: Page): Promise<void> {
  await page.click('button[type="submit"], .submit-button, [data-testid="submit"]');
}

/**
 * Reset UI state between tests - simplified
 */
export async function resetUI(page: Page): Promise<void> {
  // Close any open modals
  await page.keyboard.press('Escape');
  await page.keyboard.press('Escape');
  
  // Wait a moment for cleanup
  await page.waitForTimeout(200);
}

/**
 * Check for success notice - simplified
 */
export async function expectSuccessNotice(page: Page, timeout = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(() => {
      const notices = Array.from(document.querySelectorAll('.notice'));
      return notices.some(notice => 
        notice.textContent?.toLowerCase().includes('success') ||
        notice.textContent?.toLowerCase().includes('created') ||
        notice.textContent?.toLowerCase().includes('updated')
      );
    }, { timeout });
    return true;
  } catch {
    return false;
  }
}

/**
 * Simplified test setup for use in test files
 * Replaces the complex shared context system
 */
export async function setupTest(options?: Parameters<typeof launchObsidian>[0]): Promise<ElectronTestContext> {
  // Playwright handles worker isolation automatically
  return await launchObsidian(options);
}

/**
 * Create a test file using Obsidian's API
 */
export async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path: filePath, content }) => {
    const app = (window as any).app;
    try {
      await app.vault.create(filePath, content);
    } catch (error) {
      // File might exist, try to modify instead
      const file = app.vault.getAbstractFileByPath(filePath);
      if (file) {
        await app.vault.modify(file, content);
      } else {
        throw error;
      }
    }
  }, { path: filePath, content });
}

/**
 * Get plugin instance - simplified
 */
export async function getPlugin(page: Page): Promise<any> {
  return await page.evaluate(() => {
    return (window as any).app.plugins.plugins['ghost-sync'];
  });
}
