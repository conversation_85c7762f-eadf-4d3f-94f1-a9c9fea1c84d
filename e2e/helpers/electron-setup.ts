import type { Page, ElectronApplication } from 'playwright';
import { setupObsidianElectron, verifyPluginAvailable, resetObsidianUI } from './plugin-setup';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Shared Electron Test Setup Utility
 *
 * This module provides reusable setup functions for Playwright/Electron tests,
 * ensuring consistent test environment initialization across all test suites.
 */

export interface ElectronTestContext {
  electronApp: ElectronApplication;
  page: Page;
}

/**
 * Build plugin and update pristine vault, then restore test vault
 * This ensures tests always use the latest plugin build
 */
export async function restoreTestVaultToPristine(): Promise<void> {
  const testVaultPath = path.resolve('./tests/vault/Test');
  const pristineVaultPath = path.resolve('./tests/vault/Test.pristine');

  if (!fs.existsSync(pristineVaultPath)) {
    console.log('⚠️ Pristine vault not found, skipping restoration');
    return;
  }

  try {
    // First, build the plugin and update pristine vault
    await buildAndUpdatePristinePlugin();

    console.log('🔄 Restoring test vault to pristine state...');

    // Remove existing test vault
    if (fs.existsSync(testVaultPath)) {
      fs.rmSync(testVaultPath, { recursive: true, force: true });
    }

    // Copy pristine vault to test vault location
    await copyDirectory(pristineVaultPath, testVaultPath);

    console.log('✅ Test vault restored to pristine state');
  } catch (error) {
    console.log(`⚠️ Failed to restore test vault: ${error.message}`);
    throw error;
  }
}

/**
 * Build plugin and update pristine vault with latest build
 * Uses the unified setup script for consistency
 */
async function buildAndUpdatePristinePlugin(): Promise<void> {
  const { execSync } = await import('child_process');

  try {
    console.log('🔨 Building plugin and updating pristine vault...');

    // Use the unified setup script
    execSync('node scripts/setup-test-vault.js', {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    console.log('✅ Plugin built and pristine vault updated successfully');
  } catch (error) {
    console.log(`⚠️ Failed to build and update plugin: ${error.message}`);
    throw error;
  }
}

/**
 * Recursively copy directory
 */
async function copyDirectory(src: string, dest: string): Promise<void> {
  await fs.promises.mkdir(dest, { recursive: true });

  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}

/**
 * Standard setup for Playwright/Electron tests
 * This is the main function that test suites should use in their beforeAll hooks
 */
export async function setupElectronTest(): Promise<ElectronTestContext> {
  console.log("🔧 Setting up Electron test environment...");

  // First, restore the test vault to pristine state
  await restoreTestVaultToPristine();

  // Launch Obsidian with Playwright/Electron using standard test paths
  const { electronApp, page } = await setupObsidianElectron(
    './tests/vault/Test',
    './e2e/test_obsidian_data'
  );

  console.log("✅ Electron test environment ready");

  return { electronApp, page };
}

/**
 * Standard cleanup for Playwright/Electron tests
 * This should be used in afterAll hooks
 */
export async function cleanupElectronTest(context: ElectronTestContext): Promise<void> {
  if (context.electronApp) {
    try {
      console.log("🔄 Cleaning up Electron test environment...");
      await context.electronApp.close();
      console.log("✅ Electron test environment cleaned up");
    } catch (error) {
      console.log(`⚠️ Electron cleanup failed: ${error.message}`);
    }
  }

  // Restore vault to pristine state after tests
  try {
    await restoreTestVaultToPristine();
  } catch (error) {
    console.log(`⚠️ Failed to restore vault after cleanup: ${error.message}`);
  }
}

/**
 * Reset UI state between tests
 * This should be used in beforeEach or afterEach hooks
 */
export async function resetElectronTestUI(page: Page): Promise<void> {
  try {
    await resetObsidianUI(page);
  } catch (error) {
    console.log(`⚠️ UI reset failed: ${error.message}`);
  }
}

/**
 * Helper to create a test file using Obsidian's vault API
 */
export async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      // Create the file using Obsidian's vault API
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      // If file already exists, modify it instead
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, fileContent);
        return true;
      }
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * Helper to open a file in Obsidian
 */
export async function openFile(page: Page, filePath: string): Promise<void> {
  await page.evaluate(async ({ path }) => {
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }
    await (window as any).app.workspace.getLeaf().openFile(file);
  }, { path: filePath });
}

/**
 * Helper to wait for async operations
 */
export async function waitForOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Complete test setup with all common utilities
 * Use this for a full-featured test environment
 */
export async function setupCompleteElectronTest(): Promise<ElectronTestContext & {
  createFile: (path: string, content: string) => Promise<void>;
  openFile: (path: string) => Promise<void>;
  wait: (timeout?: number) => Promise<void>;
  resetUI: () => Promise<void>;
}> {
  const context = await setupElectronTest();

  return {
    ...context,
    createFile: (path: string, content: string) => createTestFile(context.page, path, content),
    openFile: (path: string) => openFile(context.page, path),
    wait: (timeout?: number) => waitForOperation(timeout),
    resetUI: () => resetElectronTestUI(context.page)
  };
}
