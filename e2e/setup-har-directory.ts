import * as fs from 'fs';
import * as path from 'path';

/**
 * Simple setup to ensure HAR recording directory exists
 * This replaces complex global setup with a simple directory creation
 */

const harDir = path.resolve('./e2e/recordings');

// Ensure HAR recordings directory exists
if (!fs.existsSync(harDir)) {
  fs.mkdirSync(harDir, { recursive: true });
  console.log('📁 Created HAR recordings directory');
}

// Clean up old HAR files (optional)
if (process.env.CLEAN_HAR_FILES === 'true') {
  try {
    const files = fs.readdirSync(harDir);
    const harFiles = files.filter(file => file.endsWith('.har'));
    
    for (const file of harFiles) {
      fs.unlinkSync(path.join(harDir, file));
    }
    
    if (harFiles.length > 0) {
      console.log(`🧹 Cleaned up ${harFiles.length} old HAR files`);
    }
  } catch (error) {
    console.log('⚠️ Could not clean HAR files:', error.message);
  }
}
