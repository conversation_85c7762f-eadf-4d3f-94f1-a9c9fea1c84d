import { test, beforeAll, afterAll, beforeEach, expect } from 'vitest';
import {
  setupTest,
  executeCommand,
  waitForModal,
  fillModalField,
  submitModal,
  resetUI,
  expectSuccessNotice,
  createTestFile,
  type ElectronTestContext
} from '../helpers/simplified-electron-setup';

/**
 * Example test using the simplified Playwright Electron setup
 * 
 * This demonstrates how much simpler the tests become when using
 * <PERSON><PERSON>'s native Electron capabilities
 */

describe('Simplified Electron Test Example', () => {
  let context: ElectronTestContext;

  beforeAll(async () => {
    // Much simpler setup - <PERSON><PERSON> handles worker isolation automatically
    context = await setupTest({
      recordHar: true, // Native HAR recording
      harPath: './e2e/recordings/simplified-test.har'
    });
  });

  afterAll(async () => {
    // Simple cleanup using <PERSON><PERSON>'s native capabilities
    await context.cleanup();
  });

  beforeEach(async () => {
    // Simple UI reset
    await resetUI(context.page);
  });

  test('should create a new post with simplified setup', async () => {
    const { page } = context;
    
    // Execute command - much simpler than the current approach
    await executeCommand(page, 'Ghost Sync: Create new post');
    
    // Wait for modal - simplified modal detection
    const modalAppeared = await waitForModal(page);
    expect(modalAppeared).toBe(true);
    
    // Fill form - simplified field interaction
    await fillModalField(page, 'post-title', 'Test Post from Simplified Setup');
    
    // Submit - simplified submission
    await submitModal(page);
    
    // Check for success - simplified notice detection
    const success = await expectSuccessNotice(page);
    expect(success).toBe(true);
  });

  test('should handle file operations', async () => {
    const { page } = context;
    
    // Create test file - simplified file creation
    await createTestFile(page, 'test-article.md', '# Test Article\n\nThis is a test.');
    
    // Verify file exists using Obsidian's API
    const fileExists = await page.evaluate(() => {
      const file = (window as any).app.vault.getAbstractFileByPath('test-article.md');
      return !!file;
    });
    
    expect(fileExists).toBe(true);
  });

  test('should access plugin functionality', async () => {
    const { page } = context;
    
    // Get plugin instance - simplified plugin access
    const plugin = await page.evaluate(() => {
      return (window as any).app.plugins.plugins['ghost-sync'];
    });
    
    expect(plugin).toBeDefined();
    expect(plugin.manifest?.id).toBe('ghost-sync');
  });

  test('should handle network requests with HAR recording', async () => {
    const { page } = context;
    
    // This test will automatically record network requests to the HAR file
    // No need for complex network interception setup
    
    await executeCommand(page, 'Ghost Sync: Open settings');
    
    // Any network requests made during this test will be recorded
    // in the HAR file specified in beforeAll
    
    // The HAR file can be used for:
    // - Debugging API interactions
    // - Mocking responses in future tests
    // - Performance analysis
  });
});

/**
 * Example of parallel test execution
 * Playwright handles worker isolation automatically
 */
describe('Parallel Test Example', () => {
  let context: ElectronTestContext;

  beforeAll(async () => {
    // Each worker gets its own Electron instance automatically
    context = await setupTest({
      recordHar: true,
      harPath: `./e2e/recordings/parallel-test-${process.env.VITEST_WORKER_ID || 'default'}.har`
    });
  });

  afterAll(async () => {
    await context.cleanup();
  });

  test('parallel test 1', async () => {
    // This test runs in parallel with other tests
    // Playwright ensures proper isolation
    await createTestFile(context.page, 'parallel-test-1.md', '# Parallel Test 1');
    
    const content = await context.page.evaluate(() => {
      const file = (window as any).app.vault.getAbstractFileByPath('parallel-test-1.md');
      return file ? (window as any).app.vault.read(file) : null;
    });
    
    expect(content).toContain('# Parallel Test 1');
  });

  test('parallel test 2', async () => {
    // This test also runs in parallel
    await createTestFile(context.page, 'parallel-test-2.md', '# Parallel Test 2');
    
    const content = await context.page.evaluate(() => {
      const file = (window as any).app.vault.getAbstractFileByPath('parallel-test-2.md');
      return file ? (window as any).app.vault.read(file) : null;
    });
    
    expect(content).toContain('# Parallel Test 2');
  });
});
