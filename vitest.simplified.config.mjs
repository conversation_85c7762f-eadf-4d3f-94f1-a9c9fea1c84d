import { defineConfig } from 'vitest/config';

/**
 * Simplified Vitest configuration for Playwright Electron tests
 * 
 * This configuration leverages <PERSON>wright's native capabilities:
 * - Built-in worker isolation
 * - Native process management
 * - Automatic HAR recording
 * - Standard Electron testing patterns
 */

export default defineConfig({
  test: {
    // Use node environment for Playwright tests
    environment: 'node',
    globals: true,
    
    // Include simplified tests
    include: [
      'e2e/**/*.simplified.e2e.ts',
      'e2e/specs/simplified-*.e2e.ts'
    ],
    
    // Exclude old complex setup tests during migration
    exclude: [
      'tests/**/*',
      'e2e/specs/create-new-post.e2e.ts', // Old complex test
      'e2e/specs/plugin-configuration.e2e.ts' // Old complex test
    ],
    
    // <PERSON><PERSON> handles process management, so we don't need complex teardown
    // globalTeardown: './e2e/global-teardown.ts', // Not needed with simplified setup
    
    // Reasonable timeouts - <PERSON><PERSON> handles most timing automatically
    testTimeout: 60000,  // 1 minute (reduced from 2-3 minutes)
    hookTimeout: 30000,  // 30 seconds (reduced from 1-1.5 minutes)
    
    // <PERSON><PERSON> handles worker isolation natively
    fileParallelism: true,
    maxConcurrency: 3, // Can be higher since <PERSON><PERSON> manages isolation better
    
    // Use threads with Playwright's native isolation
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false, // Playwright handles isolation, so we can use multiple threads
        isolate: true
      }
    },
    
    // Standard reporter configuration
    reporter: process.env.CI ? ['verbose', 'junit'] : 'default',
    outputFile: process.env.CI ? {
      junit: './test-results/junit-simplified.xml'
    } : undefined,
    
    // Setup for HAR recording directory
    setupFiles: ['./e2e/setup-har-directory.ts']
  },
  
  define: {
    global: 'globalThis'
  }
});
